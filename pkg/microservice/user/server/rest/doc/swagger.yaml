definitions:
  core_handler_permission.CreateRoleBindingReq:
    properties:
      identities:
        items:
          $ref: '#/definitions/types.Identity'
        type: array
      role:
        type: string
    type: object
  core_handler_permission.UpdateRoleBindingForUserReq:
    properties:
      roles:
        items:
          type: string
        type: array
    type: object
  core_handler_user.bulkUserReq:
    properties:
      uids:
        items:
          type: string
        type: array
    type: object
  core_handler_user.createUserGroupReq:
    properties:
      description:
        type: string
      name:
        type: string
      uids:
        items:
          type: string
        type: array
    type: object
  core_handler_user.listUserGroupResp:
    properties:
      rows:
        items:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.UserGroupResp'
        type: array
      total:
        type: integer
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.LoginArgs:
    properties:
      account:
        type: string
      captcha_answer:
        type: string
      captcha_id:
        type: string
      password:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.User:
    properties:
      account:
        type: string
      email:
        type: string
      group_ids:
        items:
          type: string
        type: array
      identityType:
        type: string
      name:
        type: string
      phone:
        type: string
      token:
        type: string
      uid:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.Action:
    properties:
      action:
        type: string
      alias:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingGroupInfo:
    properties:
      group_id:
        type: string
      name:
        type: string
      user_infos:
        items:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingUserInfo'
        type: array
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingUserInfo:
    properties:
      account:
        type: string
      identity_type:
        type: string
      uid:
        type: string
      username:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq:
    properties:
      actions:
        items:
          type: string
        type: array
      desc:
        type: string
      name:
        type: string
      namespace:
        type: string
      type:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.Password:
    properties:
      newPassword:
        type: string
      oldPassword:
        type: string
      uid:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.QueryArgs:
    properties:
      account:
        type: string
      identity_type:
        type: string
      name:
        type: string
      page:
        type: integer
      page_size:
        type: integer
      roles:
        items:
          type: string
        type: array
      uids:
        items:
          type: string
        type: array
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.ResourceDefinition:
    properties:
      alias:
        type: string
      resource:
        type: string
      rules:
        items:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.Action'
        type: array
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.RoleBindingResp:
    properties:
      binding_type:
        type: string
      group_info:
        $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingGroupInfo'
      roles:
        items:
          type: string
        type: array
      user_info:
        $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingUserInfo'
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.UpdateUserInfo:
    properties:
      email:
        type: string
      name:
        type: string
      phone:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.User:
    properties:
      account:
        type: string
      email:
        type: string
      name:
        type: string
      password:
        type: string
      phone:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.UserGroupResp:
    properties:
      description:
        type: string
      id:
        type: string
      name:
        type: string
      type:
        type: string
      user_total:
        type: integer
    type: object
  setting.ResourceType:
    enum:
    - system
    - custom
    type: string
    x-enum-varnames:
    - ResourceTypeSystem
    - ResourceTypeCustom
  types.DetailedRole:
    properties:
      desc:
        type: string
      id:
        type: integer
      name:
        type: string
      namespace:
        type: string
      rules:
        description: |-
          ResourceActions represents a set of verbs with its corresponding resource.
          the json response of this field `rules` is used for compatibility.
        items:
          $ref: '#/definitions/types.ResourceAction'
        type: array
      type:
        type: string
    type: object
  types.DetailedRoleTemplate:
    properties:
      desc:
        type: string
      id:
        type: integer
      name:
        type: string
      namespace:
        type: string
      rules:
        description: |-
          ResourceActions represents a set of verbs with its corresponding resource.
          the json response of this field `rules` is used for compatibility.
        items:
          $ref: '#/definitions/types.ResourceAction'
        type: array
    type: object
  types.DetailedUserGroupResp:
    properties:
      description:
        type: string
      id:
        type: string
      name:
        type: string
      type:
        type: string
      uids:
        items:
          type: string
        type: array
    type: object
  types.Identity:
    properties:
      gid:
        type: string
      identity_type:
        type: string
      uid:
        type: string
    type: object
  types.ResourceAction:
    properties:
      resource:
        type: string
      verbs:
        items:
          type: string
        type: array
    type: object
  types.Role:
    properties:
      desc:
        type: string
      id:
        type: integer
      name:
        type: string
      namespace:
        type: string
      type:
        type: string
    type: object
  types.RoleBinding:
    properties:
      name:
        type: string
      preset:
        type: boolean
      role:
        type: string
      type:
        $ref: '#/definitions/setting.ResourceType'
      uid:
        type: string
    type: object
  types.UserInfo:
    properties:
      account:
        type: string
      admin:
        type: boolean
      email:
        type: string
      identity_type:
        type: string
      last_login_time:
        type: integer
      name:
        type: string
      phone:
        type: string
      system_role_bindings:
        items:
          $ref: '#/definitions/types.RoleBinding'
        type: array
      token:
        type: string
      uid:
        type: string
    type: object
  types.UsersResp:
    properties:
      total:
        type: integer
      users:
        items:
          $ref: '#/definitions/types.UserInfo'
        type: array
    type: object
info:
  contact: {}
paths:
  /api/v1/login:
    get:
      responses: {}
    post:
      consumes:
      - application/json
      description: Authenticate a user using local login credentials.
      parameters:
      - description: Login arguments
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.LoginArgs'
      produces:
      - application/json
      responses:
        "200":
          description: Login successful
          headers:
            x-require-captcha:
              description: Indicates if captcha is required
              type: string
          schema:
            $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.User'
      summary: Local Login
      tags:
      - user
  /api/v1/policy/resource-actions:
    get:
      consumes:
      - application/json
      description: 获取资源操作定义
      parameters:
      - description: system代表系统级别，project代表项目级别
        in: query
        name: scope
        required: true
        type: string
      - description: pm or k8s
        in: query
        name: env_type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              items:
                $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.ResourceDefinition'
              type: array
            type: array
      summary: 获取资源操作定义
      tags:
      - policy
  /api/v1/policy/role-bindings:
    get:
      consumes:
      - application/json
      description: 根据项目命名空间获取角色绑定信息，支持按用户ID或用户组ID进行过滤
      parameters:
      - description: 项目命名空间
        in: query
        name: namespace
        required: true
        type: string
      - description: 用户ID，用于过滤特定用户的角色绑定
        in: query
        name: uid
        type: string
      - description: 用户组ID，用于过滤特定用户组的角色绑定
        in: query
        name: gid
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 角色绑定列表
          schema:
            items:
              $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.RoleBindingResp'
            type: array
      summary: 获取角色绑定列表
      tags:
      - policy
    post:
      consumes:
      - application/json
      description: 添加角色绑定
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/core_handler_permission.CreateRoleBindingReq'
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            type: string
      summary: 添加角色绑定
      tags:
      - policy
  /api/v1/policy/role-bindings/user/:uid:
    post:
      consumes:
      - application/json
      description: 更新用户系统角色
      parameters:
      - description: 用户ID
        in: path
        name: uid
        required: true
        type: string
      - description: 项目名称，系统角色为*
        in: query
        name: namespace
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/core_handler_permission.UpdateRoleBindingForUserReq'
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            type: string
      summary: 更新用户系统角色
      tags:
      - policy
  /api/v1/policy/role-templates:
    get:
      consumes:
      - application/json
      description: List Project Role Templates
      produces:
      - application/json
      responses:
        "200":
          description: get user info successful
          schema:
            items:
              $ref: '#/definitions/types.Role'
            type: array
      summary: List Project Role Templates
      tags:
      - policy
    post:
      consumes:
      - application/json
      description: 添加角色
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq'
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            type: string
      summary: 添加角色
      tags:
      - policy
  /api/v1/policy/role-templates/{name}:
    delete:
      consumes:
      - application/json
      description: 删除项目角色模板
      parameters:
      - description: 角色名称
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 删除项目角色模板
      tags:
      - policy
    get:
      consumes:
      - application/json
      description: GET Project Role Templates
      parameters:
      - description: 角色名称
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: get user info successful
          schema:
            $ref: '#/definitions/types.DetailedRoleTemplate'
      summary: GET Project Role Templates
      tags:
      - policy
    put:
      consumes:
      - application/json
      description: 修改角色
      parameters:
      - description: name
        in: path
        name: name
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq'
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            type: string
      summary: 修改角色
      tags:
      - policy
  /api/v1/policy/roles:
    get:
      consumes:
      - application/json
      description: List System Roles
      parameters:
      - description: 项目名, 角色时传 *
        in: query
        name: namespace
        required: true
        type: string
      - description: uid
        in: query
        name: uid
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: get user info successful
          schema:
            items:
              $ref: '#/definitions/types.Role'
            type: array
      summary: List System Roles
      tags:
      - policy
    post:
      consumes:
      - application/json
      description: 添加角色
      parameters:
      - description: project name, system role is *
        in: query
        name: namespace
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq'
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            type: string
      summary: 添加角色
      tags:
      - policy
  /api/v1/policy/roles/{name}:
    delete:
      consumes:
      - application/json
      description: 删除角色
      parameters:
      - description: role name
        in: path
        name: name
        required: true
        type: string
      - description: project name, system role is *
        in: query
        name: namespace
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            type: string
      summary: 删除角色
      tags:
      - policy
    get:
      consumes:
      - application/json
      description: 获取角色详情
      parameters:
      - description: 项目名, 角色时传 *
        in: query
        name: namespace
        required: true
        type: string
      - description: 角色名称
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/types.DetailedRole'
      summary: 获取角色详情
      tags:
      - policy
    put:
      consumes:
      - application/json
      description: 更新角色
      parameters:
      - description: role name
        in: path
        name: name
        required: true
        type: string
      - description: project name, system role is *
        in: query
        name: namespace
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq'
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            type: string
      summary: 更新角色
      tags:
      - policy
  /api/v1/user-group:
    get:
      consumes:
      - application/json
      description: 列出用户组
      parameters:
      - description: page
        in: query
        name: page
        required: true
        type: integer
      - description: page size
        in: query
        name: page_size
        required: true
        type: integer
      - description: group name
        in: query
        name: name
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/core_handler_user.listUserGroupResp'
      summary: 列出用户组
      tags:
      - group
    post:
      consumes:
      - application/json
      description: 创建用户组
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/core_handler_user.createUserGroupReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 创建用户组
      tags:
      - group
  /api/v1/user-group/{id}:
    delete:
      consumes:
      - application/json
      description: 删除用户组
      parameters:
      - description: 用户组ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 删除用户组
      tags:
      - group
    get:
      consumes:
      - application/json
      description: 获取用户组详情
      parameters:
      - description: 用户组ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/types.DetailedUserGroupResp'
      summary: 获取用户组详情
      tags:
      - group
    put:
      consumes:
      - application/json
      description: 更新用户组信息
      parameters:
      - description: 用户组ID
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/core_handler_user.createUserGroupReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 更新用户组信息
      tags:
      - group
  /api/v1/user-group/{id}/bulk-create-users:
    post:
      consumes:
      - application/json
      description: 添加用户到用户组
      parameters:
      - description: 用户组ID
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/core_handler_user.bulkUserReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 添加用户到用户组
      tags:
      - group
  /api/v1/user-group/{id}/bulk-delete-users:
    post:
      consumes:
      - application/json
      description: 移除用户到用户组
      parameters:
      - description: 用户组ID
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/core_handler_user.bulkUserReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 移除用户到用户组
      tags:
      - group
  /api/v1/users:
    post:
      consumes:
      - application/json
      description: Create a new user in the system
      parameters:
      - description: User Info
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.User'
      produces:
      - application/json
      responses:
        "200":
          description: Create user successful
          schema:
            $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.User'
      summary: Create User
      tags:
      - user
  /api/v1/users/{uid}:
    put:
      consumes:
      - application/json
      description: Update the information of a specific user by their UID
      parameters:
      - description: User ID
        in: path
        name: uid
        required: true
        type: string
      - description: User update information
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.UpdateUserInfo'
      produces:
      - application/json
      responses:
        "200":
          description: Success response
          schema:
            additionalProperties: true
            type: object
      summary: Update user information
      tags:
      - user
  /api/v1/users/{uid}/password:
    put:
      consumes:
      - application/json
      description: 更新用户密码
      parameters:
      - description: User ID
        in: path
        name: uid
        required: true
        type: string
      - description: update password
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.Password'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 更新用户密码
      tags:
      - user
  /api/v1/users/search:
    post:
      consumes:
      - application/json
      description: 获取用户列表只需要传page和per_page参数，搜索时需要再加上name参数
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.QueryArgs'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/types.UsersResp'
      summary: 获取用户列表
      tags:
      - user
  /api/v1/users/userInfo:
    get:
      consumes:
      - application/json
      description: User Info
      produces:
      - application/json
      responses:
        "200":
          description: get user info successful
          schema:
            $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.User'
      summary: User Info
      tags:
      - user
swagger: "2.0"
