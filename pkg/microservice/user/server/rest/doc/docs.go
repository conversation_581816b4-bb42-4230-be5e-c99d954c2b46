// Package doc Code generated by swaggo/swag. DO NOT EDIT
package doc

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/login": {
            "get": {
                "responses": {}
            },
            "post": {
                "description": "Authenticate a user using local login credentials.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "Local Login",
                "parameters": [
                    {
                        "description": "Login arguments",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.LoginArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Login successful",
                        "schema": {
                            "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.User"
                        },
                        "headers": {
                            "x-require-captcha": {
                                "type": "string",
                                "description": "Indicates if captcha is required"
                            }
                        }
                    }
                }
            }
        },
        "/api/v1/policy/resource-actions": {
            "get": {
                "description": "获取资源操作定义",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "policy"
                ],
                "summary": "获取资源操作定义",
                "parameters": [
                    {
                        "type": "string",
                        "description": "system代表系统级别，project代表项目级别",
                        "name": "scope",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "pm or k8s",
                        "name": "env_type",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "array",
                                "items": {
                                    "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.ResourceDefinition"
                                }
                            }
                        }
                    }
                }
            }
        },
        "/api/v1/policy/role-bindings": {
            "get": {
                "description": "根据项目命名空间获取角色绑定信息，支持按用户ID或用户组ID进行过滤",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "policy"
                ],
                "summary": "获取角色绑定列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "项目命名空间",
                        "name": "namespace",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "用户ID，用于过滤特定用户的角色绑定",
                        "name": "uid",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户组ID，用于过滤特定用户组的角色绑定",
                        "name": "gid",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "角色绑定列表",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.RoleBindingResp"
                            }
                        }
                    }
                }
            },
            "post": {
                "description": "添加角色绑定",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "policy"
                ],
                "summary": "添加角色绑定",
                "parameters": [
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/core_handler_permission.CreateRoleBindingReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "success",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/v1/policy/role-bindings/user/:uid": {
            "post": {
                "description": "更新用户系统角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "policy"
                ],
                "summary": "更新用户系统角色",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户ID",
                        "name": "uid",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "项目名称，系统角色为*",
                        "name": "namespace",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/core_handler_permission.UpdateRoleBindingForUserReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "success",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/v1/policy/role-templates": {
            "get": {
                "description": "List Project Role Templates",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "policy"
                ],
                "summary": "List Project Role Templates",
                "responses": {
                    "200": {
                        "description": "get user info successful",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/types.Role"
                            }
                        }
                    }
                }
            },
            "post": {
                "description": "添加角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "policy"
                ],
                "summary": "添加角色",
                "parameters": [
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "success",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/v1/policy/role-templates/{name}": {
            "get": {
                "description": "GET Project Role Templates",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "policy"
                ],
                "summary": "GET Project Role Templates",
                "parameters": [
                    {
                        "type": "string",
                        "description": "角色名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "get user info successful",
                        "schema": {
                            "$ref": "#/definitions/types.DetailedRoleTemplate"
                        }
                    }
                }
            },
            "put": {
                "description": "修改角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "policy"
                ],
                "summary": "修改角色",
                "parameters": [
                    {
                        "type": "string",
                        "description": "name",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "success",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            },
            "delete": {
                "description": "删除项目角色模板",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "policy"
                ],
                "summary": "删除项目角色模板",
                "parameters": [
                    {
                        "type": "string",
                        "description": "角色名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/api/v1/policy/roles": {
            "get": {
                "description": "List System Roles",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "policy"
                ],
                "summary": "List System Roles",
                "parameters": [
                    {
                        "type": "string",
                        "description": "项目名, 角色时传 *",
                        "name": "namespace",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "uid",
                        "name": "uid",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "get user info successful",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/types.Role"
                            }
                        }
                    }
                }
            },
            "post": {
                "description": "添加角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "policy"
                ],
                "summary": "添加角色",
                "parameters": [
                    {
                        "type": "string",
                        "description": "project name, system role is *",
                        "name": "namespace",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "success",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/v1/policy/roles/{name}": {
            "get": {
                "description": "获取角色详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "policy"
                ],
                "summary": "获取角色详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "项目名, 角色时传 *",
                        "name": "namespace",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "角色名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/types.DetailedRole"
                        }
                    }
                }
            },
            "put": {
                "description": "更新角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "policy"
                ],
                "summary": "更新角色",
                "parameters": [
                    {
                        "type": "string",
                        "description": "role name",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "project name, system role is *",
                        "name": "namespace",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "success",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            },
            "delete": {
                "description": "删除角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "policy"
                ],
                "summary": "删除角色",
                "parameters": [
                    {
                        "type": "string",
                        "description": "role name",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "project name, system role is *",
                        "name": "namespace",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "success",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/v1/user-group": {
            "get": {
                "description": "列出用户组",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "group"
                ],
                "summary": "列出用户组",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "page",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "page size",
                        "name": "page_size",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "group name",
                        "name": "name",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/core_handler_user.listUserGroupResp"
                        }
                    }
                }
            },
            "post": {
                "description": "创建用户组",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "group"
                ],
                "summary": "创建用户组",
                "parameters": [
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/core_handler_user.createUserGroupReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/api/v1/user-group/{id}": {
            "get": {
                "description": "获取用户组详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "group"
                ],
                "summary": "获取用户组详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户组ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/types.DetailedUserGroupResp"
                        }
                    }
                }
            },
            "put": {
                "description": "更新用户组信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "group"
                ],
                "summary": "更新用户组信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户组ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/core_handler_user.createUserGroupReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            },
            "delete": {
                "description": "删除用户组",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "group"
                ],
                "summary": "删除用户组",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户组ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/api/v1/user-group/{id}/bulk-create-users": {
            "post": {
                "description": "添加用户到用户组",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "group"
                ],
                "summary": "添加用户到用户组",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户组ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/core_handler_user.bulkUserReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/api/v1/user-group/{id}/bulk-delete-users": {
            "post": {
                "description": "移除用户到用户组",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "group"
                ],
                "summary": "移除用户到用户组",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户组ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/core_handler_user.bulkUserReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/api/v1/users": {
            "post": {
                "description": "Create a new user in the system",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "Create User",
                "parameters": [
                    {
                        "description": "User Info",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.User"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Create user successful",
                        "schema": {
                            "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.User"
                        }
                    }
                }
            }
        },
        "/api/v1/users/search": {
            "post": {
                "description": "获取用户列表只需要传page和per_page参数，搜索时需要再加上name参数",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "获取用户列表",
                "parameters": [
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.QueryArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/types.UsersResp"
                        }
                    }
                }
            }
        },
        "/api/v1/users/userInfo": {
            "get": {
                "description": "User Info",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "User Info",
                "responses": {
                    "200": {
                        "description": "get user info successful",
                        "schema": {
                            "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.User"
                        }
                    }
                }
            }
        },
        "/api/v1/users/{uid}": {
            "put": {
                "description": "Update the information of a specific user by their UID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "Update user information",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User ID",
                        "name": "uid",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "User update information",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.UpdateUserInfo"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success response",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/api/v1/users/{uid}/password": {
            "put": {
                "description": "更新用户密码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "更新用户密码",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User ID",
                        "name": "uid",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "update password",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.Password"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        }
    },
    "definitions": {
        "core_handler_permission.CreateRoleBindingReq": {
            "type": "object",
            "properties": {
                "identities": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/types.Identity"
                    }
                },
                "role": {
                    "type": "string"
                }
            }
        },
        "core_handler_permission.UpdateRoleBindingForUserReq": {
            "type": "object",
            "properties": {
                "roles": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "core_handler_user.bulkUserReq": {
            "type": "object",
            "properties": {
                "uids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "core_handler_user.createUserGroupReq": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "uids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "core_handler_user.listUserGroupResp": {
            "type": "object",
            "properties": {
                "rows": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.UserGroupResp"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.LoginArgs": {
            "type": "object",
            "properties": {
                "account": {
                    "type": "string"
                },
                "captcha_answer": {
                    "type": "string"
                },
                "captcha_id": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                }
            }
        },
        "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.User": {
            "type": "object",
            "properties": {
                "account": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "group_ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "identityType": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "token": {
                    "type": "string"
                },
                "uid": {
                    "type": "string"
                }
            }
        },
        "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.Action": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string"
                },
                "alias": {
                    "type": "string"
                }
            }
        },
        "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingGroupInfo": {
            "type": "object",
            "properties": {
                "group_id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "user_infos": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingUserInfo"
                    }
                }
            }
        },
        "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingUserInfo": {
            "type": "object",
            "properties": {
                "account": {
                    "type": "string"
                },
                "identity_type": {
                    "type": "string"
                },
                "uid": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq": {
            "type": "object",
            "properties": {
                "actions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "desc": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.Password": {
            "type": "object",
            "properties": {
                "newPassword": {
                    "type": "string"
                },
                "oldPassword": {
                    "type": "string"
                },
                "uid": {
                    "type": "string"
                }
            }
        },
        "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.QueryArgs": {
            "type": "object",
            "properties": {
                "account": {
                    "type": "string"
                },
                "identity_type": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "uids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.ResourceDefinition": {
            "type": "object",
            "properties": {
                "alias": {
                    "type": "string"
                },
                "resource": {
                    "type": "string"
                },
                "rules": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.Action"
                    }
                }
            }
        },
        "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.RoleBindingResp": {
            "type": "object",
            "properties": {
                "binding_type": {
                    "type": "string"
                },
                "group_info": {
                    "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingGroupInfo"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "user_info": {
                    "$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingUserInfo"
                }
            }
        },
        "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.UpdateUserInfo": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                }
            }
        },
        "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.User": {
            "type": "object",
            "properties": {
                "account": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                }
            }
        },
        "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.UserGroupResp": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "user_total": {
                    "type": "integer"
                }
            }
        },
        "setting.ResourceType": {
            "type": "string",
            "enum": [
                "system",
                "custom"
            ],
            "x-enum-varnames": [
                "ResourceTypeSystem",
                "ResourceTypeCustom"
            ]
        },
        "types.DetailedRole": {
            "type": "object",
            "properties": {
                "desc": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "rules": {
                    "description": "ResourceActions represents a set of verbs with its corresponding resource.\nthe json response of this field ` + "`" + `rules` + "`" + ` is used for compatibility.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/types.ResourceAction"
                    }
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "types.DetailedRoleTemplate": {
            "type": "object",
            "properties": {
                "desc": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "rules": {
                    "description": "ResourceActions represents a set of verbs with its corresponding resource.\nthe json response of this field ` + "`" + `rules` + "`" + ` is used for compatibility.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/types.ResourceAction"
                    }
                }
            }
        },
        "types.DetailedUserGroupResp": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "uids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "types.Identity": {
            "type": "object",
            "properties": {
                "gid": {
                    "type": "string"
                },
                "identity_type": {
                    "type": "string"
                },
                "uid": {
                    "type": "string"
                }
            }
        },
        "types.ResourceAction": {
            "type": "object",
            "properties": {
                "resource": {
                    "type": "string"
                },
                "verbs": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "types.Role": {
            "type": "object",
            "properties": {
                "desc": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "types.RoleBinding": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "preset": {
                    "type": "boolean"
                },
                "role": {
                    "type": "string"
                },
                "type": {
                    "$ref": "#/definitions/setting.ResourceType"
                },
                "uid": {
                    "type": "string"
                }
            }
        },
        "types.UserInfo": {
            "type": "object",
            "properties": {
                "account": {
                    "type": "string"
                },
                "admin": {
                    "type": "boolean"
                },
                "email": {
                    "type": "string"
                },
                "identity_type": {
                    "type": "string"
                },
                "last_login_time": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "system_role_bindings": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/types.RoleBinding"
                    }
                },
                "token": {
                    "type": "string"
                },
                "uid": {
                    "type": "string"
                }
            }
        },
        "types.UsersResp": {
            "type": "object",
            "properties": {
                "total": {
                    "type": "integer"
                },
                "users": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/types.UserInfo"
                    }
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "",
	Description:      "",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
